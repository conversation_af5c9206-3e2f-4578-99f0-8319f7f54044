<template>
	<div class="my-works-detail-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<div class="page-header">
				<h1>我的作品</h1>
			</div>

			<div class="content-area">
				<!-- Element Plus批量操作按钮组 -->
				<div class="batch-operations">
					<div class="controls-group" v-if="isMultiSelectMode">
						<el-checkbox
							v-model="selectAll"
							@change="handleSelectAll"
							class="select-all-checkbox custom-checkbox"
						>
							{{ selectAllText }}
						</el-checkbox>
						
						<el-button
							type="primary"
							:disabled="selectedWorks.length === 0"
							@click="handleBatchDeleteClick"
							:class="batchDeleteBtnClass"
						>
							批量删除
						</el-button>
					</div>
					
					<div class="single-button" v-else>
						<el-button
							type="primary"
							@click="handleBatchDeleteClick"
							:class="batchDeleteBtnClass"
						>
							批量删除
						</el-button>
					</div>
				</div>

				<!-- 数字人作品展示区域 -->
				<div class="works-container">
					<DigitalHumanWorks
						ref="digitalWorksRef"
						:selectedFileId="null"
						:workTab="workTab"
						:pageSize="10000"
						:enableInfiniteScroll="true"
						:hideCue="true"
						:showTextButtons="true"
						:multiSelectMode="isMultiSelectMode"
						@selection-change="handleWorkSelectionChange"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人作品组件
import DigitalHumanWorks from '@/views/modules/mySpace/myWorks/components/DigitalHumanWorks.vue'

// 工作标签状态
const workTab = ref('digital')

// 多选相关状态
const selectedWorks = ref([]) // 选中的作品数组
const selectAll = ref(false) // 全选状态
const isMultiSelectMode = ref(false) // 多选模式状态
const digitalWorksRef = ref(null) // DigitalHumanWorks组件引用

// 计算属性：批量删除按钮样式类
const batchDeleteBtnClass = computed(() => {
  return isMultiSelectMode.value ? 'batch-delete-btn active' : 'batch-delete-btn'
})

// 计算属性：全选文本显示
const selectAllText = computed(() => {
  const selectedCount = selectedWorks.value.length
  return `全选（${selectedCount}项）`
})

// 页面挂载时的初始化逻辑
onMounted(() => {
	console.log('我的作品详情页面已加载')
})

// 批量删除按钮点击处理
const handleBatchDeleteClick = () => {
  if (!isMultiSelectMode.value) {
    // 激活多选模式
    isMultiSelectMode.value = true
    selectedWorks.value = []
    selectAll.value = false
  } else {
    // 执行批量删除
    handleBatchDelete()
  }
}

// 全选功能
const handleSelectAll = (val) => {
  console.log('父组件全选方法被调用，参数:', val)
  console.log('digitalWorksRef:', digitalWorksRef.value)
  selectAll.value = val
  if (digitalWorksRef.value) {
    console.log('调用子组件的handleSelectAllWorks方法')
    // 通知子组件执行全选/取消全选
    digitalWorksRef.value.handleSelectAllWorks(val)
  } else {
    console.log('digitalWorksRef为空，无法调用子组件方法')
  }
}

// 处理子组件传来的选择变化
const handleWorkSelectionChange = (selectedWorksList) => {
  selectedWorks.value = selectedWorksList
  
  // 更新全选状态
  if (digitalWorksRef.value) {
    const totalWorks = digitalWorksRef.value.getTotalWorksCount()
    selectAll.value = totalWorks > 0 && selectedWorksList.length === totalWorks
  }
}

// 退出多选模式
const exitMultiSelectMode = () => {
  isMultiSelectMode.value = false
  selectedWorks.value = []
  selectAll.value = false
}

// 批量删除功能
const handleBatchDelete = async () => {
	if (selectedWorks.value.length === 0) {
		ElMessage.warning('请先选择要删除的作品')
		return
	}

	try {
		await ElMessageBox.confirm(
			`确定要删除选中的 ${selectedWorks.value.length} 个作品吗？`,
			'批量删除确认',
			{
				confirmButtonText: '确定删除',
				cancelButtonText: '取消',
				type: 'warning',
				confirmButtonClass: 'el-button--danger'
			}
		)

		// TODO: 这里调用删除接口
		ElMessage.success(`已删除 ${selectedWorks.value.length} 个作品`)

		// 退出多选模式并刷新数据
		exitMultiSelectMode()
		if (digitalWorksRef.value) {
			digitalWorksRef.value.refreshWorksList()
		}

	} catch (error) {
		// 用户取消删除
		console.log('用户取消删除操作')
	}
}
</script>

<style scoped lang="scss">
// 全屏应用容器
.my-works-detail-app {
	// width: 100vw;
	// height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 80px; // 大屏幕时增加左侧padding
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-x: visible; // 水平方向可见，不遮挡
	overflow-y: auto; // 垂直方向滚动
	background-color: #FFFFFF;
	display: flex;
	flex-direction: column;
}

// 页面头部
.page-header {
	margin-bottom: 20px; // 减少底部间距
	flex-shrink: 0; // 防止标题被压缩

	h1 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 32px;
		font-weight: 500;
		color: #333;
		margin: 0;
	}
}

// Element Plus批量操作按钮组样式
.batch-operations {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding: 0; // 移除左右padding
	flex-shrink: 0;
	position: relative;
	width: 1767px; // 与卡片容器宽度完全一致
	margin-left: -30px; // 与卡片容器左边距保持一致

	.controls-group {
		display: flex;
		align-items: center;
		gap: 16px;
		margin-left: auto; // 右对齐
	}

	.single-button {
		display: flex;
		align-items: center;
		margin-left: auto; // 右对齐
	}

	// 自定义圆形checkbox样式
	.custom-checkbox {
		:deep(.el-checkbox__input) {
			.el-checkbox__inner {
				width: 16px; // 全选框尺寸14x14
				height: 16px;
				border-radius: 50%; // 圆形
				border: 2px solid #d9d9d9; // 灰色边框
				background-color: #ffffff; // 白色填充
				
				&:hover {
					border-color: #0AAF60; // 悬停时变绿色
					background-color: #ffffff;
				}

				// 勾选符号样式
				&::after {
					border: 1px solid transparent; // 更细的边框
					border-left: 0;
					border-top: 0;
					content: "";
					height: 6px; // 调整勾选符号大小
					left: 4px; // 调整位置
					position: absolute;
					top: 2px;
					transform: rotate(45deg) scaleY(0);
					width: 3px;
					transition: transform 0.15s ease-in 0.05s;
					transform-origin: center;
				}
			}

			// 选中状态
			&.is-checked .el-checkbox__inner {
				background-color: #0AAF60; // 选中时绿色填充
				border-color: #0AAF60;

				// 白色勾选符号
				&::after {
					border-color: #fff;
					transform: rotate(45deg) scaleY(1);
				}
			}
		}

		:deep(.el-checkbox__label) {
			font-size: 14px; // 文字大小14px
			color: #333;
			font-weight: 500;
			margin-left: 8px !important; // 强制设置文字与checkbox间距8px
			padding-left: 0 !important; // 移除默认的padding
		}

		:deep(.el-checkbox) {
			margin-right: 0; // 移除默认的右边距
		}
	}

	.batch-delete-btn {
		background-color: #ffffff; // 默认白色背景
		border-color: #0AAF60; // 绿色边框
		color: #0AAF60; // 绿色文字

		&:hover {
			background-color: #f0f9f0; // 悬停时浅绿色背景
			border-color: #0AAF60;
			color: #0AAF60;
		}

		&:focus {
			background-color: #ffffff;
			border-color: #0AAF60;
			color: #0AAF60;
		}

		&.active {
			background-color: #0AAF60; // 激活状态为绿色背景
			border-color: #0AAF60;
			color: #ffffff; // 激活状态白色文字
			transform: scale(0.98);
			
			&:hover {
				background-color: #089a54;
				border-color: #089a54;
				color: #ffffff;
			}
		}
	}
}

// 内容区域
.content-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0; // 确保flex子项能够正确收缩
}

// 作品容器
.works-container {
	flex: 1;
	width: 1767px !important; // 保持固定宽度
	height: 100%; // 明确设置高度
	overflow: visible; // 显示所有内容，不隐藏
	display: flex;
	flex-direction: column;
}

// 隐藏DigitalHumanWorks组件中的红色元素并优化布局
.works-container {
	// 隐藏红色的失败状态标识
	:deep(.failureCue) {
		display: none !important;
	}

	// 隐藏红色的提示文本（如果有的话）
	:deep(.cue) {
		display: none !important;
	}

	// 优化DigitalHumanWorks组件的布局
	:deep(.digitalWorksBox) {
		height: 100% !important;
		display: flex;
		flex-direction: column;
	}

	:deep(.worksBox) {
		flex: 1 !important;
		height: auto !important; // 让高度自适应
		overflow-y: auto !important; // 只保留垂直滚动
		padding-right: 10px; // 为滚动条预留空间

		// 自适应宽度布局
		width: 1767px !important; // 保持固定宽度
		display: flex !important;
		flex-wrap: wrap !important;
		align-content: flex-start !important; // 内容从顶部开始排列
		gap: 20px !important; // 间距
		padding: 0 !important; // 移除内部padding
	}

	:deep(.project) {
		margin-right: 0 !important; // 移除原有的margin-right，使用gap代替
		margin-bottom: 0 !important; // 移除底部margin
		flex-shrink: 0 !important; // 防止项目被压缩
		position: relative !important; // 为选择框定位
		cursor: pointer !important; // 鼠标指针
		transition: all 0.3s ease !important; // 平滑过渡效果

		// 恢复固定尺寸的卡片
		.list,
		.mask {
			width: 231px !important; // 恢复固定宽度231px
			height: 160px !important; // 保持固定高度
		}

		// 悬停效果
		&:hover {
			.list,
			.mask {
				border: 2px solid #0AAF60 !important;
				border-radius: 0 !important; // 直角边框
			}
		}

	}
}



// // 大屏幕优化（保留基本的响应式，但worksBox保持固定宽度）
// @media (min-width: 1200px) {
// 	.main-content {
// 		padding: 20px 20px 20px 80px; // 大屏幕时增加左侧padding
// 	}

// 	.works-container {
// 		:deep(.worksBox) {
// 			width: 1767px !important; // 保持固定宽度
// 			gap: 20px !important; // 保持固定间距
// 		}
// 	}
// }
</style>